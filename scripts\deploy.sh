#!/bin/bash

# 交互式绘本应用 - 快速部署脚本
echo "🚀 开始部署交互式绘本应用..."

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo "❌ 未找到Node.js，请先安装Node.js 16+"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ 未找到npm"
    exit 1
fi

echo "✅ npm版本: $(npm --version)"

# 安装依赖
echo "📦 安装项目依赖..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 构建项目
echo "🔨 构建生产版本..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 构建完成"

# 检查是否有git
if ! command -v git &> /dev/null; then
    echo "❌ 未找到git，请先安装git"
    exit 1
fi

# 检查是否已经初始化git仓库
if [ ! -d ".git" ]; then
    echo "📝 初始化Git仓库..."
    git init
    git add .
    git commit -m "Initial commit: Interactive Storybook App"
    
    echo "🔗 请设置远程仓库地址:"
    echo "git remote add origin https://github.com/yourusername/interactive-storybook.git"
    echo "git push -u origin main"
else
    echo "✅ Git仓库已存在"
fi

# 部署选项
echo ""
echo "🌐 选择部署方式:"
echo "1. GitHub Pages (推荐)"
echo "2. Vercel"
echo "3. Netlify"
echo "4. 本地预览"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo "📚 GitHub Pages部署指南:"
        echo "1. 推送代码到GitHub仓库"
        echo "2. 在仓库Settings → Pages中启用GitHub Pages"
        echo "3. 选择GitHub Actions作为部署源"
        echo "4. 等待自动部署完成"
        echo ""
        echo "🔗 部署后访问: https://yourusername.github.io/interactive-storybook/"
        ;;
    2)
        echo "⚡ Vercel部署指南:"
        echo "1. 访问 https://vercel.com"
        echo "2. 连接GitHub账户"
        echo "3. 导入仓库并部署"
        echo "4. 获得自动生成的域名"
        ;;
    3)
        echo "🌊 Netlify部署指南:"
        echo "1. 访问 https://netlify.com"
        echo "2. 拖拽dist文件夹或连接GitHub"
        echo "3. 自动部署并获得域名"
        ;;
    4)
        echo "👀 启动本地预览..."
        npm run preview
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 部署准备完成！"
echo ""
echo "📋 下一步:"
echo "1. 配置API密钥（LIBLIB AI）"
echo "2. 测试所有功能"
echo "3. 分享应用链接"
echo ""
echo "📖 详细指南请查看: docs/deployment-guide.md"
echo "🔧 技术支持请查看: docs/DEPLOYMENT-README.md"
