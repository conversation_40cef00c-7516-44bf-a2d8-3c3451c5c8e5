# OpenAI API 测试指南

现在您已经设置了 `.env` 文件并填入了 OpenAI API 密钥，我们提供了两种方式来测试 API 是否正常工作：

## 🌐 方式1：独立测试页面

我们为您创建了一个独立的测试页面，可以直接在浏览器中测试 API 功能：

### 📁 文件位置
- `public/test-api-simple.html` - 简单的 API 测试页面

### 🚀 使用方法
1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 在浏览器中访问：
   ```
   http://localhost:5173/test-api-simple.html
   ```

3. 在页面中：
   - 输入您的 OpenAI API 密钥
   - 点击"测试 API"按钮
   - 查看测试结果

### ✅ 预期结果
如果 API 配置正确，您应该看到：
- ✅ API 连接成功
- ✅ 返回生成的文本内容
- ✅ 没有错误信息

## 🔧 方式2：在主应用中测试

您也可以直接在主应用中测试 API 功能：

### 🚀 使用方法
1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 访问主应用：
   ```
   http://localhost:5173
   ```

3. 在应用中：
   - 进入交互环节（第4、8、11页）
   - 输入回答内容
   - 点击"生成插画"按钮
   - 观察 API 调用结果

### ✅ 预期结果
如果 API 配置正确，您应该看到：
- ✅ 插画生成成功
- ✅ 新的插画显示在页面上
- ✅ 控制台没有错误信息

## 🔍 故障排除

### 常见问题

#### 1. API 密钥错误
**症状**：收到 401 Unauthorized 错误
**解决方案**：
- 检查 `.env` 文件中的 API 密钥是否正确
- 确保 API 密钥以 `sk-` 开头
- 重新启动开发服务器

#### 2. 网络连接问题
**症状**：请求超时或连接失败
**解决方案**：
- 检查网络连接
- 确认防火墙设置
- 尝试使用 VPN

#### 3. API 配额问题
**症状**：收到 429 Too Many Requests 错误
**解决方案**：
- 检查 OpenAI 账户余额
- 查看 API 使用限制
- 等待一段时间后重试

#### 4. CORS 问题
**症状**：浏览器控制台显示 CORS 错误
**解决方案**：
- 确保使用开发服务器（`npm run dev`）
- 检查 Vite 配置中的代理设置

## 📊 测试检查清单

在部署到生产环境之前，请确保以下测试都通过：

- [ ] ✅ 独立测试页面 API 调用成功
- [ ] ✅ 主应用中插画生成功能正常
- [ ] ✅ 错误处理机制工作正常
- [ ] ✅ 网络异常时有适当的提示
- [ ] ✅ API 密钥验证机制正常

## 🔐 安全注意事项

1. **不要在客户端暴露 API 密钥**
   - 生产环境中应使用服务器端代理
   - 考虑使用环境变量管理

2. **API 使用监控**
   - 定期检查 API 使用量
   - 设置使用限制和警报

3. **错误日志**
   - 记录 API 调用错误
   - 不要在日志中记录敏感信息

## 📞 获取帮助

如果测试过程中遇到问题：

1. 查看浏览器控制台的错误信息
2. 检查网络请求的详细信息
3. 参考 OpenAI API 官方文档
4. 联系技术支持

---

**注意**：测试页面仅用于开发和调试目的，请不要在生产环境中使用。
