#!/bin/bash

# 设置HTTPS的脚本
echo "🔒 开始设置HTTPS证书..."

# 检查是否已存在证书
if [ -f "cert.pem" ] && [ -f "key.pem" ]; then
    echo "✅ 证书文件已存在，跳过生成"
else
    echo "📝 生成自签名证书..."
    
    # 生成私钥
    openssl genrsa -out key.pem 2048
    
    # 生成证书签名请求配置
    cat > cert.conf <<EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Beijing
L = Beijing
O = Interactive Storybook
OU = Development
CN = *************

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = 127.0.0.1
IP.1 = *************
IP.2 = 127.0.0.1
EOF
    
    # 生成自签名证书
    openssl req -new -x509 -key key.pem -out cert.pem -days 365 -config cert.conf -extensions v3_req
    
    echo "✅ 证书生成完成"
fi

echo "🚀 证书文件："
echo "  - 私钥: key.pem"
echo "  - 证书: cert.pem"

echo ""
echo "📋 下一步："
echo "1. 运行: npm run dev"
echo "2. 访问: https://*************:5174/"
echo "3. 在浏览器中接受自签名证书警告"

echo ""
echo "⚠️  注意："
echo "- 浏览器会显示'不安全'警告，这是正常的（自签名证书）"
echo "- 点击'高级' -> '继续访问'即可"
echo "- 这样就能使用HTTPS访问，Web Crypto API将正常工作"
