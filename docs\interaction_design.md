# 自闭症儿童交互问题设计说明

## 交互问题设计原则
针对6-8岁自闭症儿童的语言表达特点，我设计了三个交互问题，遵循以下原则：

1. **开放性问题**：避免是/否或简单选择题，鼓励儿童表达完整想法
2. **情境相关**：问题与故事情境紧密相连，帮助儿童理解社交场景
3. **情感识别**：引导儿童识别和表达情感，这是自闭症儿童的常见挑战
4. **社交技能**：聚焦于友谊建立和维护的社交技能
5. **个人经验联系**：鼓励儿童将故事与自身经验联系，促进迁移学习

## 交互问题分析

### 交互环节1（第4页）
**问题**：如果你是波波，你会怎么向小兔子打招呼呢？你会说些什么来介绍自己？

**设计目的**：
- 训练社交启动技能，这是自闭症儿童常见的困难点
- 鼓励自我介绍和表达，提升语言组织能力
- 模拟陌生社交情境，帮助儿童准备现实生活中的社交互动

**评估维度**：
- 语言词汇量：能否使用适当的问候语和自我介绍词汇
- 社会适应：是否理解社交启动的适当方式
- 思维逻辑：介绍的内容是否有逻辑性和连贯性

### 交互环节2（第8页）
**问题**：波波看到这么多新朋友有点害怕。如果你是波波，你会怎么融入这个新的朋友圈子？你会先和谁说话，为什么？

**设计目的**：
- 训练群体社交情境中的决策能力
- 鼓励表达选择背后的原因，促进因果关系思考
- 帮助儿童处理社交焦虑，这是自闭症儿童常见的情绪挑战

**评估维度**：
- 思维逻辑：能否解释选择的原因
- 社会适应：是否能识别适合的社交策略
- 情感识别：是否能理解和处理焦虑情绪

### 交互环节3（第11页）
**问题**：友谊给波波带来了哪些变化？你能想到一个你和朋友一起解决问题的经历吗？请分享这个故事。

**设计目的**：
- 促进对友谊价值的理解和表达
- 鼓励个人经验叙述，这是自闭症儿童的语言挑战点
- 训练情感反思和表达能力

**评估维度**：
- 语言词汇量：能否使用丰富词汇描述经历和情感
- 思维逻辑：故事是否有清晰的时间顺序和因果关系
- 情感识别：能否表达友谊带来的情感变化
- 社会适应：能否理解合作解决问题的社交价值

## 智能引导策略
如果儿童在30秒内未回答，将采用以下引导策略：

1. **简化问题**：将开放性问题分解为更小的步骤
2. **提供选项**：给予部分选项但仍需儿童扩展回答
3. **情境提示**：回顾故事相关部分，帮助建立联系
4. **示范回答**：提供部分示范，鼓励儿童完成或改编
