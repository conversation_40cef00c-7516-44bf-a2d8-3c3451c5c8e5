#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧹 开始整理项目文件结构...');

// 定义文件夹结构
const folders = {
  'docs': '文档文件',
  'scripts': '脚本文件',
  'temp': '临时文件',
  'assets': '资源文件',
  'public/images': '公共图片',
  'src/components/ui': 'UI组件',
  'src/utils': '工具函数',
  'src/services': '服务文件',
  'src/data': '数据文件',
  'src/types': '类型定义',
  '.github/workflows': 'GitHub Actions'
};

// 创建文件夹
function createFolders() {
  console.log('📁 创建文件夹结构...');
  
  Object.keys(folders).forEach(folder => {
    const folderPath = path.join('.', folder);
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
      console.log(`✅ 创建文件夹: ${folder}`);
    }
  });
}

// 文件移动规则
const moveRules = [
  // 文档文件移动到 docs/
  {
    pattern: /\.(md)$/i,
    exclude: ['README.md', 'CHANGELOG.md'],
    destination: 'docs/'
  },
  
  // 脚本文件移动到 scripts/
  {
    pattern: /\.(sh|bat|js|py)$/i,
    exclude: ['vite.config.ts', 'tailwind.config.js', 'postcss.config.js'],
    rootOnly: true,
    destination: 'scripts/'
  },
  
  // 临时文件移动到 temp/
  {
    pattern: /^(temp_|.*\.backup|.*\.zip|.*\.old).*$/i,
    destination: 'temp/'
  },
  
  // 图片文件移动到 public/images/
  {
    pattern: /\.(png|jpg|jpeg|gif|svg)$/i,
    rootOnly: true,
    destination: 'public/images/'
  },
  
  // TypeScript定义文件移动到 src/types/
  {
    pattern: /\.d\.ts$/i,
    exclude: ['vite-env.d.ts'],
    destination: 'src/types/'
  }
];

// 需要删除的文件
const filesToDelete = [
  'full_project_package.zip',
  'interactive_storybook_web_updated.zip',
  'ui_files_package.zip',
  'updated_interactive_storybook_files.zip',
  'test-fixes.js',
  'cert.conf'
];

// 移动文件
function moveFiles() {
  console.log('📦 移动文件...');
  
  const rootFiles = fs.readdirSync('.').filter(file => {
    const stat = fs.statSync(file);
    return stat.isFile();
  });
  
  rootFiles.forEach(file => {
    moveRules.forEach(rule => {
      if (rule.pattern.test(file)) {
        // 检查排除列表
        if (rule.exclude && rule.exclude.includes(file)) {
          return;
        }
        
        // 检查是否只处理根目录文件
        if (rule.rootOnly && file.includes('/')) {
          return;
        }
        
        const sourcePath = path.join('.', file);
        const destPath = path.join('.', rule.destination, file);
        
        try {
          // 确保目标目录存在
          const destDir = path.dirname(destPath);
          if (!fs.existsSync(destDir)) {
            fs.mkdirSync(destDir, { recursive: true });
          }
          
          // 移动文件
          fs.renameSync(sourcePath, destPath);
          console.log(`📁 移动: ${file} → ${rule.destination}`);
        } catch (error) {
          console.warn(`⚠️ 移动失败: ${file} - ${error.message}`);
        }
      }
    });
  });
}

// 删除不需要的文件
function deleteFiles() {
  console.log('🗑️ 删除不需要的文件...');
  
  filesToDelete.forEach(file => {
    const filePath = path.join('.', file);
    if (fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
        console.log(`🗑️ 删除: ${file}`);
      } catch (error) {
        console.warn(`⚠️ 删除失败: ${file} - ${error.message}`);
      }
    }
  });
}

// 移动临时文件夹
function moveTempFolders() {
  console.log('📂 移动临时文件夹...');
  
  const tempFolders = [
    'temp_extract',
    'temp_interactive_updated',
    'temp_ui_extract',
    'temp_updated_extract'
  ];
  
  tempFolders.forEach(folder => {
    const sourcePath = path.join('.', folder);
    if (fs.existsSync(sourcePath)) {
      const destPath = path.join('.', 'temp', folder);
      try {
        fs.renameSync(sourcePath, destPath);
        console.log(`📂 移动文件夹: ${folder} → temp/`);
      } catch (error) {
        console.warn(`⚠️ 移动文件夹失败: ${folder} - ${error.message}`);
      }
    }
  });
}

// 创建 .gitignore 文件
function updateGitignore() {
  console.log('📝 更新 .gitignore...');
  
  const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# SSL certificates (for local development)
*.pem
*.crt
*.key
cert.conf

# Temporary files
*.tmp
*.temp
temp/

# Test files
test-*.js
test-*.html

# Backup files
*.backup
*.bak
*.old

# Local development files
scripts/setup-https.*
scripts/deploy.*

# Archive files
*.zip
*.tar.gz
*.rar
`;

  fs.writeFileSync('.gitignore', gitignoreContent);
  console.log('✅ .gitignore 已更新');
}

// 主执行函数
function main() {
  try {
    createFolders();
    moveFiles();
    moveTempFolders();
    deleteFiles();
    updateGitignore();
    
    console.log('\n🎉 项目文件整理完成！');
    console.log('\n📋 整理结果:');
    console.log('✅ 文档文件 → docs/');
    console.log('✅ 脚本文件 → scripts/');
    console.log('✅ 图片文件 → public/images/');
    console.log('✅ 临时文件 → temp/');
    console.log('✅ 类型定义 → src/types/');
    console.log('✅ 更新了 .gitignore');
    
    console.log('\n🚀 下一步:');
    console.log('1. 检查整理结果');
    console.log('2. 运行 npm run build 测试构建');
    console.log('3. 提交到 Git: git add . && git commit -m "Organize project structure"');
    console.log('4. 推送到 GitHub 并部署');
    
  } catch (error) {
    console.error('❌ 整理过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { main };
