# 交互式绘本应用使用说明

## 🎯 应用概述

这是一个专为自闭症儿童设计的交互式绘本应用，讲述小熊波波的友谊冒险故事。应用结合了AI生成插画技术，为每个用户的回答生成个性化的插画内容。

## 🚀 快速开始

### 1. 环境准备

确保您的系统已安装：
- Node.js (版本 18 或更高)
- npm 或 yarn 包管理器

### 2. 安装依赖

```bash
npm install
```

### 3. 配置API密钥

创建 `.env` 文件并添加您的LIBLIB AI API密钥：

```env
VITE_LIBLIB_ACCESS_KEY=您的AccessKey
VITE_LIBLIB_SECRET_KEY=您的SecretKey
```

### 4. 启动应用

```bash
npm run dev
```

应用将在 `http://localhost:5173` 启动。

## 📖 故事内容

### 故事概要
小熊波波是一只有点害羞的小熊，住在森林里的小木屋中。一天，他听到了美妙的歌声，决定走出舒适圈去寻找歌声的来源，最终遇到了小兔子莉莉，开始了一段美好的友谊。

### 页面结构
- **第1-3页**：故事开始，波波听到歌声
- **第4页**：交互环节 - 用户选择波波的反应
- **第5-7页**：波波遇到莉莉
- **第8页**：交互环节 - 用户选择如何回应莉莉
- **第9-10页**：友谊发展
- **第11页**：交互环节 - 用户选择如何处理困难
- **第12页**：故事结局

## 🎨 交互功能

### 语音识别
- 支持语音输入回答
- 点击麦克风图标开始录音
- 自动转换语音为文字

### AI插画生成
- 基于用户回答生成个性化插画
- 使用LIBLIB AI的Star-3 Alpha模型
- 保持角色和风格一致性

### 响应式设计
- 适配不同屏幕尺寸
- 移动端友好界面
- 清晰的视觉反馈

## 🛠️ 技术特性

### 前端技术栈
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Vite** - 构建工具

### AI集成
- **LIBLIB AI API** - 图像生成
- **Web Speech API** - 语音识别
- **自定义提示词系统** - 保持风格一致

### 部署支持
- **GitHub Pages** - 静态网站托管
- **GitHub Actions** - 自动化部署
- **环境变量管理** - 安全的API密钥处理

## 🎯 使用指南

### 对于儿童用户

1. **开始阅读**
   - 点击"开始阅读"按钮
   - 跟随故事节奏阅读

2. **参与互动**
   - 在交互页面回答问题
   - 可以使用语音或文字输入
   - 等待个性化插画生成

3. **继续故事**
   - 查看生成的插画
   - 点击"继续"按钮
   - 完成整个故事

### 对于家长/老师

1. **监督使用**
   - 陪伴孩子一起阅读
   - 引导孩子表达想法
   - 讨论故事内容和插画

2. **教育价值**
   - 培养社交技能
   - 鼓励创意表达
   - 提高语言能力

## 🔧 故障排除

### 常见问题

#### 1. 语音识别不工作
- 检查浏览器权限设置
- 确保使用HTTPS连接
- 尝试刷新页面

#### 2. 插画生成失败
- 检查网络连接
- 验证API密钥配置
- 查看浏览器控制台错误

#### 3. 页面加载缓慢
- 检查网络速度
- 清除浏览器缓存
- 尝试使用其他浏览器

### 技术支持

如果遇到技术问题：
1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 验证API配置是否正确
4. 联系技术支持团队

## 📱 浏览器兼容性

### 推荐浏览器
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 功能支持
- **语音识别**：需要现代浏览器支持
- **响应式设计**：支持所有现代浏览器
- **AI插画**：需要稳定的网络连接

## 🔒 隐私与安全

### 数据处理
- 语音数据仅用于文字转换
- 不存储个人语音记录
- API调用使用加密传输

### 安全措施
- API密钥安全存储
- HTTPS加密传输
- 无个人信息收集

## 📞 联系我们

如有任何问题或建议，请联系：
- 技术支持：[技术支持邮箱]
- 产品反馈：[产品反馈邮箱]
- 官方网站：[官方网站地址]

---

**注意**：本应用专为教育目的设计，请在成人监督下使用。
